# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
sendgrid.env

# IDE files
.vscode
.idea
*.swp
*.swo
webstorm/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
README.md

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# CI/CD
.github
Jenkinsfile

# Temporary files
tmp/
temp/

# Test files
test/

# Documentation
document/

# SSL certificates (should be mounted as volumes)
*.crt
*.key
*.pem
ssl1/
ssl2/

# Firebase credentials (should be passed as env vars)
*.json

# Data directories (should be mounted as volumes)
data/
uploads/
files/

# PM2 ecosystem
ecosystem.config.js

# Nginx config
nginx.conf
