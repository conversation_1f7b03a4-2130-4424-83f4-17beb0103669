# Docker Setup cho LBVD Project

Hướng dẫn triển khai ứng dụng LBVD sử dụng Docker và Docker Compose.

## Cấu trúc Project

```
/Volumes/T9/Projects/
├── docker-compose.yml          # File cấu hình Docker Compose chính
├── nginx/
│   └── nginx.conf             # Cấu hình Nginx reverse proxy
├── lbvd-backend-strapi/       # Strapi backend (port 1337)
│   ├── Dockerfile
│   └── .dockerignore
└── lbvd-backend/              # Express backend (port 4000)
    ├── Dockerfile
    └── .dockerignore
```

## Services

### 1. Strapi Backend (lbvd-strapi)
- **Port**: 1337
- **Database**: MySQL 8.0
- **Cache**: Redis
- **Volumes**: uploads, logs

### 2. Express Backend (lbvd-express)
- **Port**: 4000
- **Database**: MongoDB 6.0
- **Cache**: Redis (shared)
- **Volumes**: uploads, files, logs

### 3. Databases
- **MySQL**: Port 3306 (cho Strapi)
- **MongoDB**: Port 27017 (cho Express)
- **Redis**: Port 6379 (shared cache)

### 4. Nginx Reverse Proxy
- **Port**: 80, 443
- **Routes**:
  - `/admin` → Strapi Admin
  - `/api` → Strapi API
  - `/uploads` → Strapi uploads
  - `/user` → Express API
  - `/socket.io` → Express Socket.IO
  - `/files` → Express static files

## Cài đặt và Chạy

### 1. Chuẩn bị Environment Variables

Tạo file `.env` trong thư mục gốc:

```bash
# Strapi Environment
STRAPI_JWT_SECRET=your-strapi-jwt-secret-here
STRAPI_ADMIN_JWT_SECRET=your-strapi-admin-jwt-secret-here
STRAPI_APP_KEYS=your-strapi-app-keys-here
STRAPI_API_TOKEN_SALT=your-strapi-api-token-salt-here

# Express Environment
EXPRESS_JWT_SECRET=your-express-jwt-secret-here
EXPRESS_SESSION_SECRET=your-express-session-secret-here

# Database Passwords
MYSQL_ROOT_PASSWORD=your-mysql-root-password
MYSQL_PASSWORD=your-mysql-password
MONGO_ROOT_PASSWORD=your-mongo-root-password
```

### 2. Build và Chạy Services

```bash
# Chuyển đến thư mục chứa docker-compose.yml
cd /Volumes/T9/Projects

# Build và chạy tất cả services
docker-compose up -d

# Hoặc build lại từ đầu
docker-compose up -d --build

# Xem logs
docker-compose logs -f

# Xem logs của service cụ thể
docker-compose logs -f strapi-backend
docker-compose logs -f express-backend
```

### 3. Kiểm tra Services

```bash
# Kiểm tra trạng thái containers
docker-compose ps

# Kiểm tra health
curl http://localhost/health

# Truy cập Strapi Admin
open http://localhost/admin

# Test Express API
curl http://localhost/user/api/test
```

## Quản lý Services

### Dừng Services
```bash
docker-compose down
```

### Dừng và xóa volumes
```bash
docker-compose down -v
```

### Restart service cụ thể
```bash
docker-compose restart strapi-backend
docker-compose restart express-backend
```

### Xem logs realtime
```bash
docker-compose logs -f --tail=100
```

### Truy cập container
```bash
# Truy cập Strapi container
docker-compose exec strapi-backend sh

# Truy cập Express container
docker-compose exec express-backend sh

# Truy cập database
docker-compose exec mysql mysql -u strapi_user -p strapi_db
docker-compose exec mongo mongosh
```

## Backup và Restore

### Backup Database
```bash
# Backup MySQL (Strapi)
docker-compose exec mysql mysqldump -u strapi_user -p strapi_db > backup_strapi.sql

# Backup MongoDB (Express)
docker-compose exec mongo mongodump --db lbvd_db --out /backup
```

### Restore Database
```bash
# Restore MySQL
docker-compose exec -T mysql mysql -u strapi_user -p strapi_db < backup_strapi.sql

# Restore MongoDB
docker-compose exec mongo mongorestore --db lbvd_db /backup/lbvd_db
```

## Monitoring

### Xem resource usage
```bash
docker stats
```

### Xem disk usage
```bash
docker system df
```

### Clean up
```bash
# Xóa unused images
docker image prune

# Xóa unused volumes
docker volume prune

# Xóa tất cả unused resources
docker system prune -a
```

## Troubleshooting

### 1. Port conflicts
Nếu port đã được sử dụng, thay đổi port mapping trong docker-compose.yml:
```yaml
ports:
  - "1338:1337"  # Thay vì 1337:1337
```

### 2. Permission issues
```bash
# Fix permission cho uploads
sudo chown -R 1000:1000 ./uploads
```

### 3. Database connection issues
```bash
# Kiểm tra network
docker network ls
docker network inspect projects_lbvd-network
```

### 4. Memory issues
Tăng memory limit cho Docker Desktop hoặc thêm vào docker-compose.yml:
```yaml
deploy:
  resources:
    limits:
      memory: 1G
```

## Production Deployment

### 1. SSL Configuration
Thêm SSL certificates vào `nginx/ssl/` và cập nhật nginx.conf

### 2. Environment Variables
Sử dụng Docker secrets hoặc external config management

### 3. Scaling
```bash
# Scale Express backend
docker-compose up -d --scale express-backend=3
```

### 4. Health Checks
Thêm health checks vào docker-compose.yml:
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:1337/admin"]
  interval: 30s
  timeout: 10s
  retries: 3
```
