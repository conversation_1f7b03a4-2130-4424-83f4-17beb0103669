import { Component, ViewEncapsulation, HostBinding, Input, OnInit } from '@angular/core';

import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FooterService } from '../../../core/services/footer.service';
@Component({
  selector: 'app-footer-content',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule, RouterModule],
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterContentComponent implements OnInit {
  @HostBinding('style.display') display = 'contents';

  // Dữ liệu từ API
  socialLinks: any[] = [];
  appStoreLinks: any[] = [];
  webConfig: any = {
    email: '',
    phone: '',
    about: '',
    logoUrl: '',
  };
  isLoading = true;

  constructor(private footerService: FooterService) {}

  ngOnInit() {
    this.loadFooterData();
  }

  /** Value props */
  @Input() image616: string = '';
  /** Style props */
  @Input() frameFooterPadding: string | number = '';
  @Input() rectangleDivHeight: string | number = '';

  /**
   * Load dữ liệu footer từ API
   */
  private loadFooterData(): void {
    // Load social links và app store links
    this.footerService.getProcessedFooterData().subscribe({
      next: data => {
        this.socialLinks = data.socialLinks;
        this.appStoreLinks = data.appStoreLinks;
      },
      error: error => {
        console.error('Lỗi khi tải dữ liệu social links:', error);
        // Fallback to default social data nếu API lỗi
        this.setDefaultSocialData();
      },
    });

    // Load web config data
    this.footerService.getProcessedWebConfig().subscribe({
      next: data => {
        this.webConfig = data;
        this.isLoading = false;
      },
      error: error => {
        console.error('Lỗi khi tải dữ liệu web config:', error);
        this.isLoading = false;
        // Fallback to default web config nếu API lỗi
        this.setDefaultWebConfig();
      },
    });
  }

  /**
   * Set dữ liệu social mặc định khi API lỗi
   */
  private setDefaultSocialData(): void {
    this.socialLinks = [
      { name: 'Facebook', url: '#', iconUrl: 'assets/<EMAIL>' },
      { name: 'TikTok', url: '#', iconUrl: 'assets/<EMAIL>' },
      { name: 'YouTube', url: '#', iconUrl: 'assets/<EMAIL>' },
    ];

    this.appStoreLinks = [
      { name: 'App Store', url: '#', iconUrl: 'assets/<EMAIL>' },
      { name: 'Google Play', url: '#', iconUrl: 'assets/<EMAIL>' },
    ];
  }

  /**
   * Set dữ liệu web config mặc định khi API lỗi
   */
  private setDefaultWebConfig(): void {
    this.webConfig = {
      email: '<EMAIL>',
      phone: '0839887878',
      about:
        'Làng Nuôi Biển Vân đồn là nơi nuôi trồng thủy hải sản chất lượng nhất cả nước. Hệ sinh thái tổ hợp Làng Nuôi Biển Vân Đồn tại Hà Nội là không gian văn hóa ẩm thực mang phong cách trải nghiệm và giao lưu mang tinh hoa biển cả trong từng món ăn.',
      logoUrl: 'assets/<EMAIL>',
    };
  }

  get frameFooterStyle() {
    return {
      padding: this.frameFooterPadding,
    };
  }

  get rectangleDiv3Style() {
    return {
      height: this.rectangleDivHeight,
    };
  }
}
