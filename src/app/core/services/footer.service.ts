import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

export interface SocialConfig {
  id: number;
  attributes: {
    name: string;
    url: string;
    type: 'social' | 'appstore';
    icon?: {
      data?: {
        attributes: {
          url: string;
        };
      };
    };
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
}

export interface FooterData {
  data: SocialConfig[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface WebConfigData {
  data: {
    id: number;
    attributes: {
      frontend_footer_email: string | null;
      frontend_footer_phone: string | null;
      frontend_footer_about: string | null;
      logo_white?: {
        data?: {
          attributes: {
            url: string;
          };
        };
      };
      createdAt: string;
      updatedAt: string;
      publishedAt: string;
    };
  };
  meta: any;
}

export interface ProcessedFooterData {
  socialLinks: {
    name: string;
    url: string;
    iconUrl?: string;
  }[];
  appStoreLinks: {
    name: string;
    url: string;
    iconUrl?: string;
  }[];
}

export interface ProcessedWebConfig {
  email: string;
  phone: string;
  about: string;
  logoUrl: string;
}

@Injectable({
  providedIn: 'root',
})
export class FooterService {
  private readonly API_BASE_URL = environment.apiUrlV2;
  private readonly API_ENDPOINT = 'api/web-social-configs';
  private readonly API_WEB_CONFIG = 'api/web-trang-chu';

  constructor(private http: HttpClient) {}

  /**
   * Lấy dữ liệu footer từ API
   * @returns Observable<FooterData>
   */
  getFooterData(): Observable<FooterData> {
    const url = `${this.API_BASE_URL}/${this.API_ENDPOINT}?populate=*`;

    return this.http.get<FooterData>(url);
  }

  /**
   * Lấy và xử lý dữ liệu footer
   * @returns Observable<ProcessedFooterData>
   */
  getProcessedFooterData(): Observable<ProcessedFooterData> {
    return this.getFooterData().pipe(
      map((response: FooterData) => {
        const socialLinks: any[] = [];
        const appStoreLinks: any[] = [];

        if (response.data && Array.isArray(response.data)) {
          response.data.forEach(item => {
            const config = {
              name: item.attributes.name,
              url: item.attributes.url,
              iconUrl: item.attributes.icon?.data?.attributes?.url || '',
            };

            if (item.attributes.type === 'social') {
              socialLinks.push(config);
            } else if (item.attributes.type === 'appstore') {
              appStoreLinks.push(config);
            }
          });
        }

        return {
          socialLinks,
          appStoreLinks,
        };
      }),
    );
  }

  /**
   * Lấy danh sách social links
   * @returns Observable<any[]>
   */
  getSocialLinks(): Observable<any[]> {
    return this.getProcessedFooterData().pipe(map(data => data.socialLinks));
  }

  /**
   * Lấy danh sách app store links
   * @returns Observable<any[]>
   */
  getAppStoreLinks(): Observable<any[]> {
    return this.getProcessedFooterData().pipe(map(data => data.appStoreLinks));
  }

  /**
   * Lấy dữ liệu web config từ API
   * @returns Observable<WebConfigData>
   */
  getWebConfig(): Observable<WebConfigData> {
    const url = `${this.API_BASE_URL}/${this.API_WEB_CONFIG}?populate=*`;

    return this.http.get<WebConfigData>(url);
  }

  /**
   * Lấy và xử lý dữ liệu web config
   * @returns Observable<ProcessedWebConfig>
   */
  getProcessedWebConfig(): Observable<ProcessedWebConfig> {
    return this.getWebConfig().pipe(
      map((response: WebConfigData) => {
        const attributes = response.data.attributes;

        return {
          email: attributes.frontend_footer_email || '<EMAIL>',
          phone: attributes.frontend_footer_phone || '0839887878',
          about:
            attributes.frontend_footer_about ||
            'Làng Nuôi Biển Vân đồn là nơi nuôi trồng thủy hải sản chất lượng nhất cả nước. Hệ sinh thái tổ hợp Làng Nuôi Biển Vân Đồn tại Hà Nội là không gian văn hóa ẩm thực mang phong cách trải nghiệm và giao lưu mang tinh hoa biển cả trong từng món ăn.',
          logoUrl: attributes.logo_white?.data?.attributes?.url || 'assets/<EMAIL>',
        };
      }),
    );
  }
}
