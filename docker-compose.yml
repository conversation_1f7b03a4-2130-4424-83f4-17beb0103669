version: '3.8'

services:
  # Strapi Backend Service
  strapi-backend:
    build:
      context: ./lbvd-backend-strapi
      dockerfile: Dockerfile
    container_name: lbvd-strapi
    restart: unless-stopped
    ports:
      - "1337:1337"
    environment:
      - NODE_ENV=production
      - DATABASE_CLIENT=mysql
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - DATABASE_NAME=strapi_db
      - DATABASE_USERNAME=strapi_user
      - DATABASE_PASSWORD=strapi_password
      - JWT_SECRET=your-jwt-secret-here
      - ADMIN_JWT_SECRET=your-admin-jwt-secret-here
      - APP_KEYS=your-app-keys-here
      - API_TOKEN_SALT=your-api-token-salt-here
    volumes:
      - strapi_uploads:/app/public/uploads
      - strapi_logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - lbvd-network

  # Express Backend Service
  express-backend:
    build:
      context: ./lbvd-backend
      dockerfile: Dockerfile
    container_name: lbvd-express
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/lbvd_db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your-jwt-secret-here
      - SESSION_SECRET=your-session-secret-here
    volumes:
      - express_uploads:/app/uploads
      - express_files:/app/files
      - express_logs:/app/logs
    depends_on:
      - mongo
      - redis
    networks:
      - lbvd-network

  # MySQL Database cho Strapi
  mysql:
    image: mysql:8.0
    container_name: lbvd-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=strapi_db
      - MYSQL_USER=strapi_user
      - MYSQL_PASSWORD=strapi_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - lbvd-network

  # MongoDB Database cho Express
  mongo:
    image: mongo:6.0
    container_name: lbvd-mongo
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin_password
      - MONGO_INITDB_DATABASE=lbvd_db
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"
    networks:
      - lbvd-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: lbvd-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - lbvd-network

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: lbvd-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - strapi-backend
      - express-backend
    networks:
      - lbvd-network

volumes:
  mysql_data:
  mongo_data:
  redis_data:
  strapi_uploads:
  strapi_logs:
  express_uploads:
  express_files:
  express_logs:

networks:
  lbvd-network:
    driver: bridge
