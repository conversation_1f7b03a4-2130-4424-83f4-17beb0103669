# Dockerfile cho Express Backend
FROM node:18-alpine

# Cài đặt các dependencies cần thiết cho canvas và các package native
RUN apk add --no-cache \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    python3 \
    make \
    g++

# Thiết lập thư mục làm việc
WORKDIR /app

# Sao chép package.json và yarn.lock
COPY package*.json ./
COPY yarn.lock ./

# Cài đặt dependencies
RUN npm ci --only=production

# Sao chép source code
COPY . .

# Tạo các thư mục cần thiết
RUN mkdir -p uploads files logs

# Expose port
EXPOSE 4000

# Khởi chạy ứng dụng
CMD ["npm", "start"]
