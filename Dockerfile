# Dockerfile cho Strapi Backend
FROM node:18-alpine

# <PERSON><PERSON><PERSON><PERSON> lập thư mục làm việc
WORKDIR /app

# Sao chép package.json và package-lock.json
COPY package*.json ./

# Cài đặt dependencies
RUN npm ci --only=production

# Sao chép source code
COPY . .

# T<PERSON><PERSON> thư mục uploads nếu chưa có
RUN mkdir -p public/uploads

# Build Strapi
RUN npm run build-prod

# Expose port
EXPOSE 1337

# Khởi chạy ứng dụng
CMD ["npm", "run", "s-prod"]
